{"name": "any-listen", "version": "0.1.0", "scripts": {"dev": "pnpm dev:desktop", "dev:desktop": "pnpm -F @shared/scripts dev:desktop", "dev:web": "pnpm -F @shared/scripts dev:web", "build:theme": "pnpm -F @shared/theme build:theme", "build": "pnpm build:desktop", "build:dir": "pnpm -F @shared/scripts build:desktop:dir", "build:desktop": "pnpm -F @shared/scripts build:desktop", "build:web": "pnpm -F @shared/scripts build:web", "pub:web": "pnpm -F @shared/publish pub web-server", "pub:desk": "pnpm -F @shared/publish pub desktop", "cldep": "rm -rf node_modules && pnpm -r exec rm -rf node_modules"}, "engines": {"node": ">=22.12.0 || ^20.19.0"}, "workspaces": ["packages/*"], "pnpm": {"ignoredBuiltDependencies": [], "onlyBuiltDependencies": ["better-sqlite3", "electron", "esbuild", "svelte-preprocess"]}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@^", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.14"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}
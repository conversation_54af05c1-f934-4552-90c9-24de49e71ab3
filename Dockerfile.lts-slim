FROM node:lts-slim AS base

FROM base AS builder

WORKDIR /source-code

RUN apt-get update && \
    apt-get install -y \
    python3 \
    build-essential \
    git

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV WEB_SERVER_ONLY="true"
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
RUN npm install corepack -g && corepack enable pnpm && pnpm fetch

COPY . ./
RUN pnpm i --offline --frozen-lockfile \
    && pnpm build:web

FROM base AS final
WORKDIR /server

RUN npm r npm -g
RUN apt-get update && \
    apt-get install -y tzdata && \
    rm -rf /var/lib/apt/lists/*

COPY --from=builder ./source-code/build ./

# VOLUME /server/data
ENV DATA_PATH="/server/data"

# https://en.wikipedia.org/wiki/List_of_tz_database_time_zones#List
# ENV TZ=Asia/Shanghai
EXPOSE 9500
ENV NODE_ENV="production"
ENV PORT="9500"
ENV BIND_IP="0.0.0.0"
# ENV PROXY_HEADER 'x-real-ip'
# ENV SERVER_NAME 'My Sync Server'
# ENV MAX_SNAPSHOT_NUM '10'
# ENV LIST_ADD_MUSIC_LOCATION_TYPE 'top'
# ENV LX_USER_user1 '123.123'
# ENV LX_USER_user2 '{ "password": "123.456", "maxSnapshotNum": 10, "list.addMusicLocationType": "top" }'
# ENV CONFIG_PATH '/server/config.js'
# ENV LOG_PATH '/server/logs'
# ENV DATA_PATH '/server/data'

CMD [ "node", "index.cjs" ]

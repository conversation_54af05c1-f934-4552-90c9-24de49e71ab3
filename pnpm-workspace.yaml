packages:
  # all packages in direct subdirs of packages/
  - 'packages/*'
  - 'packages/shared/*'
  - 'packages/extensions/*'
  # all packages in subdirs of components/
  # - 'components/**'
  # exclude packages that are inside test directories
  - '!**/test/**'

overrides:
  '@types/ws': '8.5.4'
  'depd': 'latest'
  'node-abi': 'latest'
  'json5': 'latest'
  'semver': 'latest'
  'minimatch': 'latest'
  'micromatch': 'latest'
  'ws>bufferutil': '-'
  'ws>utf-8-validate': '-'

# https://github.com/pnpm/pnpm/issues/8378
publicHoistPattern:
  - '*eslint*'

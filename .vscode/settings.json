{
  "prettier.useEditorConfig": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.stylelint": "explicit",
    "source.fixAll.eslint": "explicit"
  },
  "stylelint.validate": ["css", "less", "postcss"],
  "eslint.validate": ["svelte", "javascript", "typescript"],
  "i18n-ally.localesPaths": ["packages/shared/i18n/langs"],
  // "i18n-ally.fullReloadOnChanged": true,
  "i18n-ally.keystyle": "flat",
  "i18n-ally.displayLanguage": "zh-cn",
  "i18n-ally.sourceLanguage": "zh-cn",
  "i18n-ally.translate.engines": ["deepl", "google-cn", "google", "openai", "libretranslate"],
  "i18n-ally.sortKeys": true,
  "javascript.preferences.importModuleSpecifier": "non-relative",
  "typescript.tsdk": "packages/shared/eslint/node_modules/typescript/lib",
  "i18n-ally.review.enabled": false,
  "i18n-ally.review.gutters": false
}

{
  "extends": "./node_modules/@any-listen/eslint/tsconfig.json",
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "composite": true,
    "target": "ESNext",
    "useDefineForClassFields": true,
    "moduleResolution": "Bundler",
    "module": "ESNext",
    /**
     * Typecheck JS in `.svelte` and `.js` files by default.
     * Disable checkJs if you'd like to use dynamic types in JS.
     * Note that setting allowJs false does not prevent the use
     * of JS in `.svelte` files.
     */
    "allowJs": true,
    "checkJs": true,
    "isolatedModules": true,
    "outDir": "../../dist",
    "baseUrl": "./src",
    "paths": {
      "@/*": ["./*"]
    },
    "types": ["vite/client", "@any-listen/types"]
  },
  "include": [
    "src/preload/**/*.ts",
    "src/preload/**/*.js",
    "src/app/renderer/winMain/preload/**/*.js",
    "src/app/renderer/winMain/preload/**/*.ts"
  ],
  "exclude": ["@types/node"]
}

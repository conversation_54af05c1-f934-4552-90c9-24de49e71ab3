{"version": "0.2.0-beta.6", "name": "web-server", "main": "build-config/vite.config.ts", "scripts": {"build": "vite build"}, "repository": {"type": "git", "url": "git+https://github.com/any-listen/any-listen-web-server.git"}, "dependencies": {"@any-listen/app": "workspace:@shared/app@*", "@any-listen/common": "workspace:@shared/common@*", "@any-listen/i18n": "workspace:@shared/i18n@*", "@any-listen/nodejs": "workspace:@shared/nodejs@*", "@any-listen/theme": "workspace:@shared/theme@*", "@any-listen/web": "workspace:@shared/web@*", "@koa/cors": "^5.0.0", "better-sqlite3": "^11.10.0", "jsonwebtoken": "^9.0.2", "koa": "^3.0.0", "koa-router": "^13.0.1", "log4js": "6.9.1", "lru-cache": "^11.1.0", "message2call": "^2.0.3", "mime": "^4.0.7", "ws": "^8.18.2"}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/types": "workspace:@shared/types@*", "@types/jsonwebtoken": "^9.0.9", "@types/koa": "^2.15.0", "@types/koa-router": "^7.4.8", "@types/koa__cors": "^5.0.0", "@types/ws": "8.18.1", "vite": "^6.3.5"}}
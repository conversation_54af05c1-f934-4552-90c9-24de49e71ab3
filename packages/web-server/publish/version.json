{"version": "0.1.0", "desc": "First version 🎉", "time": "2025-01-26T00:00:00.000Z", "history": [], "beta": [{"version": "0.2.0-beta.6", "desc": "Fixes\n- Fixed lyrics scrolling\n\n修复\n- 修复歌词滚动", "time": "2025-05-31T02:36:54.516Z"}, {"version": "0.2.0-beta.5", "desc": "Add\n- Add play bar style settings\n- Add extension icon display\n- Add online extension store list loading and online extension installation, upgrade\n- Add basic playback details page\n- Add settings for playback details in Settings - Playback Details, and options for displaying lyric translations and romaji in Playback Settings\n\n新增\n- 新增播放栏样式设置\n- 添加扩展图标显示\n- 添加在线扩展商店列表加载与在线扩展安装、升级\n- 添加基础的播放详情页\n- 添加设置-播放详情设置，播放设置-显示歌词翻译、罗马音设置", "time": "2025-05-31T01:47:56.740Z"}, {"version": "0.2.0-beta.4", "desc": "Fixes\n- Fixed playlist synchronization issues\n- Fixed issues with updating the played list\n\n修复\n- 修复播放列表同步问题\n- 修复已播放列表更新问题", "time": "2025-05-18T03:48:02.891Z"}, {"version": "0.2.0-beta.3", "desc": "Add\n- Improved proxy handling\n- Add icon in Nav\n\nOptimization\n- Update scroll handler for better performance\n\nFixes\n- Fix latest version display\n\n---\n\n新增\n- 完善代理处理\n- 在导航中添加了图标\n\n优化\n- 更新滚动处理以提高性能\n\n修复\n- 修复了最新版本显示问题", "time": "2025-05-11T03:22:09.899Z"}, {"version": "0.2.0-beta.2", "desc": "Fixes\n- Fixed armv7l image build issue.\n\n---\n\n修复\n- 修复 armv7l 镜像构建", "time": "2025-05-05T11:32:01.346Z"}, {"version": "0.2.0-beta.1", "desc": "Add\n- Added the ability to add songs by selecting a folder, which will scan the selected directory and its subdirectories for songs.\n\nOptimization\n- Optimized the UI of the version check popup and fixed issues with displaying new version content.\n- Improved the appearance of the virtual scrollbar.\n\nFixes\n- Fixed the issue where notification bubbles were obscured by pop-up layers.\n\n---\n\n新增\n- 新增选择文件夹的方式添加歌曲，将会扫描所选目录及子目录内的歌曲\n\n优化\n- 优化版本检查弹窗 UI 及修复新版本内容显示问题\n- 优化虚拟滚动条显示效果\n\n修复\n- 修复通知气泡被弹出层遮挡的问题", "time": "2025-05-05T09:25:17.361Z"}, {"version": "0.2.0-beta.0", "desc": "Add\n- Add extension management\n- Add version check\n\nFix\n- Fix flac file lyric read\n- Fix allow public path `/`\n- Fix Safari play bar Music Pic size\n\n---\n\n添加\n- 添加扩展管理\n- 添加版本检查\n\n修复\n- 修复 Flac 文件歌词读取问题\n- 修复 `allowPublicDir` 为 `/` 时出现的问题\n- 修复 Safari 浏览器播放栏音乐图片大小问题", "time": "2025-04-24T15:13:09.371Z"}]}
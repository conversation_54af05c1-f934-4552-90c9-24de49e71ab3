declare namespace AnyListen {
  interface Config {
    /**
     * 绑定的端口
     */
    port: string
    /**
     * 要绑定的IP， 0.0.0.0 接受所有IP的请求
     */
    bindIp: string
    /**
     * 是否启用 HTTP 请求日志记录
     */
    httpLog: boolean
    /**
     * 是否启用跨域功能
     */
    'cors.enabled': boolean
    /**
     * 允许跨域的域名，空数组则允许所有域名跨域
     */
    'cors.whitelist': string[]

    /**
     * 服务名称
     */
    serverName: string

    /**
     * 是否使用代理转发请求到本服务器
     */
    'proxy.enabled': boolean

    /**
     * 代理转发的请求头
     */
    'proxy.header': string

    /**
     * 允许选择的系统路径范围
     */
    allowPublicDir: string[]

    /**
     * 登录密码
     */
    password: string
  }
}

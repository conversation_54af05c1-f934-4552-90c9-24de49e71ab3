{"name": "view-main", "scripts": {"dev:web": "vite --mode web", "dev:desktop": "vite --mode desktop", "build:web": "vite build --mode web", "build:desktop": "vite build --mode desktop", "check": "svelte-check --tsconfig ./tsconfig.json"}, "type": "module", "main": "vite.config.js", "dependencies": {"@any-listen/common": "workspace:@shared/common@*", "@any-listen/i18n": "workspace:@shared/i18n@*", "@any-listen/web": "workspace:@shared/web@*", "hast-util-to-html": "^9.0.5", "message2call": "^2.0.3", "refractor": "^5.0.0", "regexparam": "^3.0.0", "sortablejs": "^1.15.6"}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/types": "workspace:@shared/types@*", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@swc/html": "^1.11.29", "@types/postcss-pxtorem": "^6.1.0", "@types/sortablejs": "^1.15.8", "eslint-plugin-svelte": "^3.9.0", "less": "^4.3.0", "postcss": "^8.5.3", "postcss-pxtorem": "^6.1.0", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-less": "^3.0.1", "svelte": "^5.33.4", "svelte-check": "^4.2.1", "svelte-eslint-parser": "^1.2.0", "svelte-preprocess": "^6.0.3", "vite": "^6.3.5", "vite-plugin-svg-icons": "^2.0.1"}}
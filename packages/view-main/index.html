<!doctype html>
<html style="background-color: transparent">
  <head>
    <title>Any Listen</title>
    <!-- <meta name="theme-color" content="#fff" /> -->
  </head>
  <body id="body" style="background-color: transparent">
    <div id="root" style="display: none"></div>
    <script>
      {
        const formatLang = (lang = 'en') => {
          if (lang === 'zh-cn') return 'zh-Hans'
          if (lang === 'zh-tw') return 'zh-Hant'
          return lang.split('-')[0]
        }
        window.setLang = (lang = navigator.language.toLocaleLowerCase()) => {
          document.documentElement.setAttribute('lang', formatLang(lang))
        }
        window.setLang()
        const initScrollbarWidth = () => {
          let scrollDiv = document.createElement('div')
          scrollDiv.style.cssText = `position:absolute;top:-9999px;width:30px;height:30px;overflow:scroll;`
          document.body.appendChild(scrollDiv)
          const scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth
          document.body.removeChild(scrollDiv)
          scrollDiv = null
          document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`)
          document.documentElement.style.setProperty('--scrollbar-width-negative', `${-scrollbarWidth}px`)
        }
        initScrollbarWidth()
        window.dom_style = document.createElement('style')
        document.body.appendChild(window.dom_style)
        window.setTheme = (colors) => {
          window.dom_style.innerText = `:root {${Object.entries(colors)
            .map(([key, value]) => `${key}:${value};`)
            .join('')}}`

          // document.querySelector('meta[name="theme-color"]')?.setAttribute('content', colors['--color-primary-light-1000'])
        }
        //<%- envScript %>
      }
    </script>
    <script type="module" src="src/main.ts"></script>
  </body>
</html>

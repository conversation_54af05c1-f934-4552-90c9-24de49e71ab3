import { useSettingValue } from '@/modules/setting/reactive.svelte'
import { getFontSizeWithScreen } from '@/shared'
import { IPC_CODE } from '@any-listen/common/constants'
import { appEvent } from './store/event'
import { appState } from './store/state'

export const useAppAeady = () => {
  let appAeady = $state.raw(false)

  const unsubscribe = appEvent.on('connected', () => {
    appAeady = true
  })
  const unsubscribe2 = appEvent.on('desconnected', () => {
    appAeady = false
  })

  $effect(() => {
    return () => {
      unsubscribe()
      unsubscribe2()
    }
  })

  return {
    get appAeady() {
      return appAeady
    },
  }
}

export const useShowLogin = () => {
  let showLogin = $state.raw(false)

  // 模拟已连接状态，跳过登录
  showLogin = false;

  return {
    get showLogin() {
      return showLogin
    },
  }
}

export const useIsFullscreen = () => {
  let isFullscreen = $state.raw(appState.isFullscreen)

  const unsubscribe = appEvent.on('fullscreen', (val) => {
    isFullscreen = val
  })

  $effect(() => {
    return () => {
      unsubscribe()
    }
  })

  return {
    get isFullscreen() {
      return isFullscreen
    },
  }
}

export const useListItemHeight = (height: number) => {
  const fontSize = useSettingValue('common.fontSize')
  let listItemHeight = $state.raw(Math.ceil((appState.isFullscreen ? getFontSizeWithScreen() : fontSize.val) * height))

  $effect(() => {
    return appEvent.on('fullscreen', (val) => {
      listItemHeight = Math.ceil((val ? getFontSizeWithScreen() : fontSize.val) * height)
    })
  })
  return {
    get val() {
      return listItemHeight
    },
  }
}

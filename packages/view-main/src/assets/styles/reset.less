@layer reset {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  html,
  body,
  div,
  span,
  applet,
  object,
  iframe,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  blockquote,
  pre,
  a,
  abbr,
  acronym,
  address,
  big,
  cite,
  code,
  del,
  dfn,
  em,
  img,
  ins,
  kbd,
  q,
  s,
  samp,
  small,
  strike,
  strong,
  sub,
  sup,
  tt,
  var,
  b,
  u,
  i,
  center,
  dl,
  dt,
  dd,
  ol,
  ul,
  li,
  fieldset,
  form,
  label,
  legend,
  table,
  caption,
  tbody,
  tfoot,
  thead,
  tr,
  th,
  td,
  article,
  aside,
  canvas,
  details,
  embed,
  figure,
  figcaption,
  footer,
  header,
  hgroup,
  menu,
  nav,
  output,
  ruby,
  section,
  summary,
  time,
  mark,
  audio,
  video {
    padding: 0;
    margin: 0;
    font: inherit;
    vertical-align: baseline;
    border: 0;
  }
  input,
  button,
  textarea,
  select {
    font: inherit;
    color: currentcolor;
    word-spacing: inherit;
    letter-spacing: inherit;
  }

  /* HTML5 display-role reset for older browsers */
  article,
  aside,
  details,
  figcaption,
  figure,
  footer,
  header,
  hgroup,
  menu,
  nav,
  section {
    display: block;
  }
  // html {
  // }
  body {
    line-height: 1.2;
  }
  ol,
  ul {
    list-style: none;
  }
  blockquote,
  q {
    quotes: none;
  }
  blockquote::before,
  blockquote::after,
  q::before,
  q::after {
    content: '';
    content: none;
  }
  table {
    border-spacing: 0;
    border-collapse: collapse;
  }

  @media (prefers-color-scheme: dark) {
    html.web {
      background-color: #3c3c3c !important;
    }
  }

  // https://github.com/microsoft/vscode/blob/2dd0bca3954d4c03c427d6b447205b68817bd000/src/vs/workbench/browser/media/style.css

  /* Font Families (with CJK support) */

  .windows {
    font-family: 'Segoe WPC', 'Segoe UI', sans-serif;
  }
  .windows:lang(zh-Hans) {
    font-family: 'Microsoft YaHei', 'Segoe WPC', 'Segoe UI', sans-serif;
  }
  .windows:lang(zh-Hant) {
    font-family: 'Microsoft Jhenghei', 'Segoe WPC', 'Segoe UI', sans-serif;
  }
  .windows:lang(ja) {
    font-family: 'Yu Gothic UI', 'Meiryo UI', 'Segoe WPC', 'Segoe UI', sans-serif;
  }
  .windows:lang(ko) {
    font-family: 'Malgun Gothic', Dotom, 'Segoe WPC', 'Segoe UI', sans-serif;
  }

  .mac {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  }
  .mac:lang(zh-Hans) {
    font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  }
  .mac:lang(zh-Hant) {
    font-family: -apple-system, BlinkMacSystemFont, 'PingFang TC', sans-serif;
  }
  .mac:lang(ja) {
    font-family: -apple-system, BlinkMacSystemFont, 'Hiragino Kaku Gothic Pro', sans-serif;
  }
  .mac:lang(ko) {
    font-family: -apple-system, BlinkMacSystemFont, 'Apple SD Gothic Neo', 'Nanum Gothic', AppleGothic, sans-serif;
  }

  /* Linux: add `system-ui` as first font and not `Ubuntu` to allow other distribution pick their standard OS font */
  .linux {
    font-family: system-ui, Ubuntu, 'Droid Sans', sans-serif;
  }
  .linux:lang(zh-Hans) {
    font-family: system-ui, Ubuntu, 'Droid Sans', 'Source Han Sans SC', 'Source Han Sans CN', 'Source Han Sans', sans-serif;
  }
  .linux:lang(zh-Hant) {
    font-family: system-ui, Ubuntu, 'Droid Sans', 'Source Han Sans TC', 'Source Han Sans TW', 'Source Han Sans', sans-serif;
  }
  .linux:lang(ja) {
    font-family: system-ui, Ubuntu, 'Droid Sans', 'Source Han Sans J', 'Source Han Sans JP', 'Source Han Sans', sans-serif;
  }
  .linux:lang(ko) {
    font-family:
      system-ui, Ubuntu, 'Droid Sans', 'Source Han Sans K', 'Source Han Sans JR', 'Source Han Sans', UnDotum, 'FBaekmuk Gulim',
      sans-serif;
  }
}

.mac .code {
  font-family: 'SF Mono', monaco, menlo, courier, monospace;
}
.windows .code {
  font-family: consolas, 'Courier New', monospace;
}
.linux .code {
  font-family: 'Ubuntu Mono', 'Liberation Mono', 'DejaVu Sans Mono', 'Courier New', monospace;
}

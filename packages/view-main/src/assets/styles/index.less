@import './reset.less';
// @import './animate.less';
@import './mixin.less';

*,
*::after,
*::before {
  -webkit-user-drag: none;
}

:root {
  --color-primary: rgb(77 175 124);
  --color-primary-alpha-100: rgb(77 175 124 / 90%);
  --color-primary-alpha-200: rgb(77 175 124 / 80%);
  --color-primary-alpha-300: rgb(77 175 124 / 70%);
  --color-primary-alpha-400: rgb(77 175 124 / 60%);
  --color-primary-alpha-500: rgb(77 175 124 / 50%);
  --color-primary-alpha-600: rgb(77 175 124 / 40%);
  --color-primary-alpha-700: rgb(77 175 124 / 30%);
  --color-primary-alpha-800: rgb(77 175 124 / 20%);
  --color-primary-alpha-900: rgb(77 175 124 / 10%);
  --color-primary-dark-100: rgb(69 158 112);
  --color-primary-dark-100-alpha-100: rgb(69 158 112 / 90%);
  --color-primary-dark-100-alpha-200: rgb(69 158 112 / 80%);
  --color-primary-dark-100-alpha-300: rgb(69 158 112 / 70%);
  --color-primary-dark-100-alpha-400: rgb(69 158 112 / 60%);
  --color-primary-dark-100-alpha-500: rgb(69 158 112 / 50%);
  --color-primary-dark-100-alpha-600: rgb(69 158 112 / 40%);
  --color-primary-dark-100-alpha-700: rgb(69 158 112 / 30%);
  --color-primary-dark-100-alpha-800: rgb(69 158 112 / 20%);
  --color-primary-dark-100-alpha-900: rgb(69 158 112 / 10%);
  --color-primary-dark-200: rgb(62 142 101);
  --color-primary-dark-200-alpha-100: rgb(62 142 101 / 90%);
  --color-primary-dark-200-alpha-200: rgb(62 142 101 / 80%);
  --color-primary-dark-200-alpha-300: rgb(62 142 101 / 70%);
  --color-primary-dark-200-alpha-400: rgb(62 142 101 / 60%);
  --color-primary-dark-200-alpha-500: rgb(62 142 101 / 50%);
  --color-primary-dark-200-alpha-600: rgb(62 142 101 / 40%);
  --color-primary-dark-200-alpha-700: rgb(62 142 101 / 30%);
  --color-primary-dark-200-alpha-800: rgb(62 142 101 / 20%);
  --color-primary-dark-200-alpha-900: rgb(62 142 101 / 10%);
  --color-primary-dark-300: rgb(56 128 91);
  --color-primary-dark-300-alpha-100: rgb(56 128 91 / 90%);
  --color-primary-dark-300-alpha-200: rgb(56 128 91 / 80%);
  --color-primary-dark-300-alpha-300: rgb(56 128 91 / 70%);
  --color-primary-dark-300-alpha-400: rgb(56 128 91 / 60%);
  --color-primary-dark-300-alpha-500: rgb(56 128 91 / 50%);
  --color-primary-dark-300-alpha-600: rgb(56 128 91 / 40%);
  --color-primary-dark-300-alpha-700: rgb(56 128 91 / 30%);
  --color-primary-dark-300-alpha-800: rgb(56 128 91 / 20%);
  --color-primary-dark-300-alpha-900: rgb(56 128 91 / 10%);
  --color-primary-dark-400: rgb(50 115 82);
  --color-primary-dark-400-alpha-100: rgb(50 115 82 / 90%);
  --color-primary-dark-400-alpha-200: rgb(50 115 82 / 80%);
  --color-primary-dark-400-alpha-300: rgb(50 115 82 / 70%);
  --color-primary-dark-400-alpha-400: rgb(50 115 82 / 60%);
  --color-primary-dark-400-alpha-500: rgb(50 115 82 / 50%);
  --color-primary-dark-400-alpha-600: rgb(50 115 82 / 40%);
  --color-primary-dark-400-alpha-700: rgb(50 115 82 / 30%);
  --color-primary-dark-400-alpha-800: rgb(50 115 82 / 20%);
  --color-primary-dark-400-alpha-900: rgb(50 115 82 / 10%);
  --color-primary-dark-500: rgb(45 104 74);
  --color-primary-dark-500-alpha-100: rgb(45 104 74 / 90%);
  --color-primary-dark-500-alpha-200: rgb(45 104 74 / 80%);
  --color-primary-dark-500-alpha-300: rgb(45 104 74 / 70%);
  --color-primary-dark-500-alpha-400: rgb(45 104 74 / 60%);
  --color-primary-dark-500-alpha-500: rgb(45 104 74 / 50%);
  --color-primary-dark-500-alpha-600: rgb(45 104 74 / 40%);
  --color-primary-dark-500-alpha-700: rgb(45 104 74 / 30%);
  --color-primary-dark-500-alpha-800: rgb(45 104 74 / 20%);
  --color-primary-dark-500-alpha-900: rgb(45 104 74 / 10%);
  --color-primary-dark-600: rgb(41 94 67);
  --color-primary-dark-600-alpha-100: rgb(41 94 67 / 90%);
  --color-primary-dark-600-alpha-200: rgb(41 94 67 / 80%);
  --color-primary-dark-600-alpha-300: rgb(41 94 67 / 70%);
  --color-primary-dark-600-alpha-400: rgb(41 94 67 / 60%);
  --color-primary-dark-600-alpha-500: rgb(41 94 67 / 50%);
  --color-primary-dark-600-alpha-600: rgb(41 94 67 / 40%);
  --color-primary-dark-600-alpha-700: rgb(41 94 67 / 30%);
  --color-primary-dark-600-alpha-800: rgb(41 94 67 / 20%);
  --color-primary-dark-600-alpha-900: rgb(41 94 67 / 10%);
  --color-primary-dark-700: rgb(37 85 60);
  --color-primary-dark-700-alpha-100: rgb(37 85 60 / 90%);
  --color-primary-dark-700-alpha-200: rgb(37 85 60 / 80%);
  --color-primary-dark-700-alpha-300: rgb(37 85 60 / 70%);
  --color-primary-dark-700-alpha-400: rgb(37 85 60 / 60%);
  --color-primary-dark-700-alpha-500: rgb(37 85 60 / 50%);
  --color-primary-dark-700-alpha-600: rgb(37 85 60 / 40%);
  --color-primary-dark-700-alpha-700: rgb(37 85 60 / 30%);
  --color-primary-dark-700-alpha-800: rgb(37 85 60 / 20%);
  --color-primary-dark-700-alpha-900: rgb(37 85 60 / 10%);
  --color-primary-dark-800: rgb(33 77 54);
  --color-primary-dark-800-alpha-100: rgb(33 77 54 / 90%);
  --color-primary-dark-800-alpha-200: rgb(33 77 54 / 80%);
  --color-primary-dark-800-alpha-300: rgb(33 77 54 / 70%);
  --color-primary-dark-800-alpha-400: rgb(33 77 54 / 60%);
  --color-primary-dark-800-alpha-500: rgb(33 77 54 / 50%);
  --color-primary-dark-800-alpha-600: rgb(33 77 54 / 40%);
  --color-primary-dark-800-alpha-700: rgb(33 77 54 / 30%);
  --color-primary-dark-800-alpha-800: rgb(33 77 54 / 20%);
  --color-primary-dark-800-alpha-900: rgb(33 77 54 / 10%);
  --color-primary-dark-900: rgb(30 69 49);
  --color-primary-dark-900-alpha-100: rgb(30 69 49 / 90%);
  --color-primary-dark-900-alpha-200: rgb(30 69 49 / 80%);
  --color-primary-dark-900-alpha-300: rgb(30 69 49 / 70%);
  --color-primary-dark-900-alpha-400: rgb(30 69 49 / 60%);
  --color-primary-dark-900-alpha-500: rgb(30 69 49 / 50%);
  --color-primary-dark-900-alpha-600: rgb(30 69 49 / 40%);
  --color-primary-dark-900-alpha-700: rgb(30 69 49 / 30%);
  --color-primary-dark-900-alpha-800: rgb(30 69 49 / 20%);
  --color-primary-dark-900-alpha-900: rgb(30 69 49 / 10%);
  --color-primary-dark-1000: rgb(27 62 44);
  --color-primary-dark-1000-alpha-100: rgb(27 62 44 / 90%);
  --color-primary-dark-1000-alpha-200: rgb(27 62 44 / 80%);
  --color-primary-dark-1000-alpha-300: rgb(27 62 44 / 70%);
  --color-primary-dark-1000-alpha-400: rgb(27 62 44 / 60%);
  --color-primary-dark-1000-alpha-500: rgb(27 62 44 / 50%);
  --color-primary-dark-1000-alpha-600: rgb(27 62 44 / 40%);
  --color-primary-dark-1000-alpha-700: rgb(27 62 44 / 30%);
  --color-primary-dark-1000-alpha-800: rgb(27 62 44 / 20%);
  --color-primary-dark-1000-alpha-900: rgb(27 62 44 / 10%);
  --color-primary-light-100: rgb(113 191 150);
  --color-primary-light-100-alpha-100: rgb(113 191 150 / 90%);
  --color-primary-light-100-alpha-200: rgb(113 191 150 / 80%);
  --color-primary-light-100-alpha-300: rgb(113 191 150 / 70%);
  --color-primary-light-100-alpha-400: rgb(113 191 150 / 60%);
  --color-primary-light-100-alpha-500: rgb(113 191 150 / 50%);
  --color-primary-light-100-alpha-600: rgb(113 191 150 / 40%);
  --color-primary-light-100-alpha-700: rgb(113 191 150 / 30%);
  --color-primary-light-100-alpha-800: rgb(113 191 150 / 20%);
  --color-primary-light-100-alpha-900: rgb(113 191 150 / 10%);
  --color-primary-light-200: rgb(141 204 171);
  --color-primary-light-200-alpha-100: rgb(141 204 171 / 90%);
  --color-primary-light-200-alpha-200: rgb(141 204 171 / 80%);
  --color-primary-light-200-alpha-300: rgb(141 204 171 / 70%);
  --color-primary-light-200-alpha-400: rgb(141 204 171 / 60%);
  --color-primary-light-200-alpha-500: rgb(141 204 171 / 50%);
  --color-primary-light-200-alpha-600: rgb(141 204 171 / 40%);
  --color-primary-light-200-alpha-700: rgb(141 204 171 / 30%);
  --color-primary-light-200-alpha-800: rgb(141 204 171 / 20%);
  --color-primary-light-200-alpha-900: rgb(141 204 171 / 10%);
  --color-primary-light-300: rgb(164 214 188);
  --color-primary-light-300-alpha-100: rgb(164 214 188 / 90%);
  --color-primary-light-300-alpha-200: rgb(164 214 188 / 80%);
  --color-primary-light-300-alpha-300: rgb(164 214 188 / 70%);
  --color-primary-light-300-alpha-400: rgb(164 214 188 / 60%);
  --color-primary-light-300-alpha-500: rgb(164 214 188 / 50%);
  --color-primary-light-300-alpha-600: rgb(164 214 188 / 40%);
  --color-primary-light-300-alpha-700: rgb(164 214 188 / 30%);
  --color-primary-light-300-alpha-800: rgb(164 214 188 / 20%);
  --color-primary-light-300-alpha-900: rgb(164 214 188 / 10%);
  --color-primary-light-400: rgb(182 222 201);
  --color-primary-light-400-alpha-100: rgb(182 222 201 / 90%);
  --color-primary-light-400-alpha-200: rgb(182 222 201 / 80%);
  --color-primary-light-400-alpha-300: rgb(182 222 201 / 70%);
  --color-primary-light-400-alpha-400: rgb(182 222 201 / 60%);
  --color-primary-light-400-alpha-500: rgb(182 222 201 / 50%);
  --color-primary-light-400-alpha-600: rgb(182 222 201 / 40%);
  --color-primary-light-400-alpha-700: rgb(182 222 201 / 30%);
  --color-primary-light-400-alpha-800: rgb(182 222 201 / 20%);
  --color-primary-light-400-alpha-900: rgb(182 222 201 / 10%);
  --color-primary-light-500: rgb(197 229 212);
  --color-primary-light-500-alpha-100: rgb(197 229 212 / 90%);
  --color-primary-light-500-alpha-200: rgb(197 229 212 / 80%);
  --color-primary-light-500-alpha-300: rgb(197 229 212 / 70%);
  --color-primary-light-500-alpha-400: rgb(197 229 212 / 60%);
  --color-primary-light-500-alpha-500: rgb(197 229 212 / 50%);
  --color-primary-light-500-alpha-600: rgb(197 229 212 / 40%);
  --color-primary-light-500-alpha-700: rgb(197 229 212 / 30%);
  --color-primary-light-500-alpha-800: rgb(197 229 212 / 20%);
  --color-primary-light-500-alpha-900: rgb(197 229 212 / 10%);
  --color-primary-light-600: rgb(209 234 221);
  --color-primary-light-600-alpha-100: rgb(209 234 221 / 90%);
  --color-primary-light-600-alpha-200: rgb(209 234 221 / 80%);
  --color-primary-light-600-alpha-300: rgb(209 234 221 / 70%);
  --color-primary-light-600-alpha-400: rgb(209 234 221 / 60%);
  --color-primary-light-600-alpha-500: rgb(209 234 221 / 50%);
  --color-primary-light-600-alpha-600: rgb(209 234 221 / 40%);
  --color-primary-light-600-alpha-700: rgb(209 234 221 / 30%);
  --color-primary-light-600-alpha-800: rgb(209 234 221 / 20%);
  --color-primary-light-600-alpha-900: rgb(209 234 221 / 10%);
  --color-primary-light-700: rgb(218 238 228);
  --color-primary-light-700-alpha-100: rgb(218 238 228 / 90%);
  --color-primary-light-700-alpha-200: rgb(218 238 228 / 80%);
  --color-primary-light-700-alpha-300: rgb(218 238 228 / 70%);
  --color-primary-light-700-alpha-400: rgb(218 238 228 / 60%);
  --color-primary-light-700-alpha-500: rgb(218 238 228 / 50%);
  --color-primary-light-700-alpha-600: rgb(218 238 228 / 40%);
  --color-primary-light-700-alpha-700: rgb(218 238 228 / 30%);
  --color-primary-light-700-alpha-800: rgb(218 238 228 / 20%);
  --color-primary-light-700-alpha-900: rgb(218 238 228 / 10%);
  --color-primary-light-800: rgb(225 241 233);
  --color-primary-light-800-alpha-100: rgb(225 241 233 / 90%);
  --color-primary-light-800-alpha-200: rgb(225 241 233 / 80%);
  --color-primary-light-800-alpha-300: rgb(225 241 233 / 70%);
  --color-primary-light-800-alpha-400: rgb(225 241 233 / 60%);
  --color-primary-light-800-alpha-500: rgb(225 241 233 / 50%);
  --color-primary-light-800-alpha-600: rgb(225 241 233 / 40%);
  --color-primary-light-800-alpha-700: rgb(225 241 233 / 30%);
  --color-primary-light-800-alpha-800: rgb(225 241 233 / 20%);
  --color-primary-light-800-alpha-900: rgb(225 241 233 / 10%);
  --color-primary-light-900: rgb(231 244 237);
  --color-primary-light-900-alpha-100: rgb(231 244 237 / 90%);
  --color-primary-light-900-alpha-200: rgb(231 244 237 / 80%);
  --color-primary-light-900-alpha-300: rgb(231 244 237 / 70%);
  --color-primary-light-900-alpha-400: rgb(231 244 237 / 60%);
  --color-primary-light-900-alpha-500: rgb(231 244 237 / 50%);
  --color-primary-light-900-alpha-600: rgb(231 244 237 / 40%);
  --color-primary-light-900-alpha-700: rgb(231 244 237 / 30%);
  --color-primary-light-900-alpha-800: rgb(231 244 237 / 20%);
  --color-primary-light-900-alpha-900: rgb(231 244 237 / 10%);
  --color-primary-light-1000: rgb(255 255 255);
  --color-primary-light-1000-alpha-100: rgb(255 255 255 / 90%);
  --color-primary-light-1000-alpha-200: rgb(255 255 255 / 80%);
  --color-primary-light-1000-alpha-300: rgb(255 255 255 / 70%);
  --color-primary-light-1000-alpha-400: rgb(255 255 255 / 60%);
  --color-primary-light-1000-alpha-500: rgb(255 255 255 / 50%);
  --color-primary-light-1000-alpha-600: rgb(255 255 255 / 40%);
  --color-primary-light-1000-alpha-700: rgb(255 255 255 / 30%);
  --color-primary-light-1000-alpha-800: rgb(255 255 255 / 20%);
  --color-primary-light-1000-alpha-900: rgb(255 255 255 / 10%);
  --color-theme: rgb(77 175 124);
  // --color-scrollbar-track:

  // --color-900: #fff;
  // --color-800: #fafafa;
  // --color-700: #f5f5f5;
  // --color-600: #eeeeee;
  // --color-500: #e0e0e0;
  // --color-400: #bdbdbd;
  // --color-300: #9e9e9e;
  // --color-200: #757575;
  // --color-100: #616161;
  // --color-050: #424242;
  // --color-000: #212121;
  // --color-000: #fff;
  // --color-050: #fafafa;
  // --color-100: #f5f5f5;
  // --color-200: #eeeeee;
  // --color-300: #e0e0e0;
  // --color-400: #bdbdbd;
  // --color-500: #9e9e9e;
  // --color-600: #757575;
  // --color-700: #616161;
  // --color-800: #424242;
  // --color-900: #212121;

  --color-000: rgb(255 255 255);
  --color-050: rgb(244 244 244);
  --color-100: rgb(233 233 233);
  --color-150: rgb(222 222 222);
  --color-200: rgb(211 211 211);
  --color-250: rgb(200 200 200);
  --color-300: rgb(188 188 188);
  --color-350: rgb(177 177 177);
  --color-400: rgb(166 166 166);
  --color-450: rgb(155 155 155);
  --color-500: rgb(144 144 144);
  --color-550: rgb(133 133 133);
  --color-600: rgb(122 122 122);
  --color-650: rgb(111 111 111);
  --color-700: rgb(100 100 100);
  --color-750: rgb(89 89 89);
  --color-800: rgb(77 77 77);
  --color-850: rgb(66 66 66);
  --color-900: rgb(55 55 55);
  --color-950: rgb(44 44 44);
  --color-1000: rgb(33 33 33);
  --color-app-background: var(--color-primary-light-900-alpha-900);
  --color-main-background: rgb(255 255 255 / 90%);
  --color-nav-font: var(--color-primary);
  // --color-app-background: rgba(0, 0, 0, .5);
  // --color-main-background: rgba(0, 0, 0, 0.26);
  --color-btn-hide: #3bc2b2;
  --color-btn-min: #85c43b;
  // --color-btn-max: #e7aa36;
  --color-btn-close: #fab4a0;
  --color-badge-primary: var(--color-primary);
  --color-badge-secondary: #4baed5;
  --color-badge-tertiary: #e7aa36;
  --color-font: var(--color-850);
  // --color-font-label: var(--color-primary-dark-1000-alpha-600);
  --color-font-label: var(--color-450);
  --color-primary-font: var(--color-primary);
  --color-primary-font-hover: var(--color-primary-alpha-300);
  --color-primary-font-active: var(--color-primary-dark-100-alpha-200);
  --color-primary-background: var(--color-primary-light-400-alpha-700);
  --color-primary-background-hover: var(--color-primary-light-300-alpha-700);
  --color-primary-background-selected: var(--color-primary-alpha-900);
  --color-primary-background-active: var(--color-primary-light-200-alpha-700);
  --color-button-font: var(--color-primary-alpha-100);
  --color-button-font-selected: var(--color-primary-dark-100-alpha-100);
  --color-button-background: var(--color-primary-light-400-alpha-700);
  --color-button-background-selected: var(--color-primary-alpha-800);
  --color-button-background-hover: var(--color-primary-light-300-alpha-600);
  --color-button-background-active: var(--color-primary-light-100-alpha-600);
  --color-border: var(--color-primary-alpha-900);
  --color-content-background: var(--color-primary-light-1000);
  --background-image: none;
  --background-image-position: center;
  --background-image-size: cover;
  --background-blur: 0;
}

html {
  font-size: 16px;
}

small {
  font-size: 0.8em;
}
.small {
  font-size: 0.9em;
}
.tip {
  color: var(--color-label);
}
strong {
  font-weight: bold;
}
.nobreak {
  white-space: nowrap;
}
.auto-hidden {
  .mixin-ellipsis-1();
}
.break {
  word-break: break-all;
}
.select {
  user-select: text;
}
.no-select {
  user-select: none;
}
.underline {
  text-decoration: underline;
}

svg {
  transition: @transition-normal;
  transition-property: fill;
}

button,
input,
textarea,
a {
  color: var(--color-font);
}
button {
  svg {
    filter: drop-shadow(0 0 2px rgb(0 0 0 / 20%));
  }
}

::placeholder {
  color: var(--color-font-label);
}
::selection {
  color: var(--color-primary-dark-500-alpha-200);
  background: var(--color-primary-light-100-alpha-500);
}

.hover,
a {
  cursor: pointer;
  transition: color 0.2s ease;
  &:hover {
    color: var(--color-primary-font-hover);
  }
  &:active {
    color: var(--color-primary-font-active);
  }
}

.scroll,
textarea {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
  }
  &::-webkit-scrollbar-track {
    background-color: var(--color-primary-light-100-alpha-800);
    border-radius: 3px;
    // background-color: rgba(0, 0, 0, 0.1);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--color-primary-alpha-600);
    border-radius: 3px;
    // background-color: rgba(0, 0, 0, 0.2);
    transition: background-color 0.4s ease;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-primary-alpha-400);
    border-radius: 3px;
    // background-color: rgba(0, 0, 0, 0.4);
    transition: background-color 0.4s ease;
  }
}
textarea::-webkit-resizer {
  border-color: transparent var(--color-primary-light-100-alpha-600) var(--color-primary-light-100-alpha-600) transparent;
  border-style: solid;
  border-width: 3px;
}

.custom-scrollbar {
  position: absolute;
  top: 0;
  right: 4px;
  width: 8px;
  height: 100%;
  background-color: var(--color-primary-light-100-alpha-800);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 1.6s ease-out;

  &:hover,
  &.visible {
    opacity: 1;
    transition: opacity 0.3s ease;
  }
}
.custom-scrollbar-thumb {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--color-primary-alpha-600);
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--color-primary-alpha-400);
  }
}

.thead {
  flex: none;
  padding-right: 6px;
  border-bottom: var(--color-border);

  .num {
    .nobreak();
    .center();

    color: var(--color-font-label);
  }
  // box-shadow: 0 0 2px var(--color-primary-dark-500-alpha-800);
  // position: relative;
  // z-index: 2;
}
table {
  width: 100%;
  overflow: hidden;
  color: var(--color-font);
  border-spacing: 0;
  border-collapse: collapse;
  th {
    padding: 0 6px;
    font-size: 12px;
    line-height: 38px;
    text-align: left;
  }
}

// .list {
//   flex: auto;
//   width: 100%;
//   overflow: hidden;
//   color: var(--color-font);

//   .list-item {
//     // border-bottom: 1px solid @color-theme_2-line;
//     box-sizing: border-box;
//     display: flex;
//     flex-flow: row nowrap;
//     align-items: center;
//     height: 100%;
//     font-size: 12px;
//     // border-top: 1px solid rgba(0, 0, 0, 0.12);
//     transition: 0.2s ease;
//     transition-property: background-color, color;

//     &:hover {
//       background-color: var(--color-primary-background-hover);

//       // .list-item-cell-action {
//       //   display: block;
//       // }
//     }
//     &.active {
//       background-color: var(--color-primary-background-active);
//     }
//     &.selected {
//       background-color: var(--color-primary-background-hover);
//     }
//     &.disabled {
//       opacity: .5;
//     }
//     .list-item-cell {
//       position: relative;
//       box-sizing: border-box;
//       flex: none;
//       padding: 0 6px;
//       // transition:  0.3s cubic-bezier(0.4, 0, 0.2, 1);
//       line-height: 16px;
//       vertical-align: middle;
//       .mixin-ellipsis-1();

//       &.auto {
//         flex: auto;
//       }

//       &.num, .num {
//         .nobreak;
//         .center;

//         padding-right: 3px;
//         padding-left: 3px;
//         font-size: 11px;
//         color: var(--color-font-label);
//       }

//       &.name {
//         display: flex;
//         flex-flow: row nowrap;
//         align-items: center;
//         overflow: hidden;
//         text-overflow: initial;
//         white-space: initial;

//         >.name {
//           .mixin-ellipsis-1();
//         }
//       }
//       .badge {
//         margin-left: 3px;
//         opacity: .85;
//       }
//     }

//     // .list-item-cell-action {
//     //   white-space: nowrap;
//     //   display: none;
//     //   flex: auto;
//     //   text-align: right;
//     //   // position: absolute;
//     //   // right: 5px;
//     //   // top: -2px;
//     //   // opacity: 0;
//     //   // transition: opacity .1s ease;
//     // }
//   }
// }

.copying {
  .no-select {
    display: none !important;
  }
}

.gap-left {
  + .gap-left {
    margin-left: 20px;
  }
}
.gap-top {
  &.top {
    margin-top: 25px;
  }

  + .gap-top {
    margin-top: 10px;
  }
}

.color-picker {
  border-radius: @radius-border !important;
}

.list-active-enter-active,
.list-active-leave-active {
  transition: 0.13s ease;
  transition-property: width, opacity;
}

.list-active-enter-from,
.list-active-leave-to {
  width: 0.25em !important;
  opacity: 0;
}

.play-active-enter-active,
.play-active-leave-active {
  transition: 0.13s ease;
  transition-property: transform, opacity;
}

.play-active-enter-from,
.play-active-leave-to {
  opacity: 0;
  transform: scale(0.3);
}

.no-animation * {
  transition: none !important;
  animation: none !important;
}

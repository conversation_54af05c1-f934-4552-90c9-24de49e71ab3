{"name": "@shared/extension-preload", "scripts": {"build:web": "vite build --mode web", "build:desktop": "vite build --mode desktop", "build:mobile": "vite --mode mobile"}, "types": "./src/types/global.d.ts", "main": "vite.config.ts", "dependencies": {"@any-listen/common": "workspace:@shared/common@*", "message2call": "^2.0.3"}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/types": "workspace:@shared/types@*", "vite": "^6.3.5"}}
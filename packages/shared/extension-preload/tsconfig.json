{"extends": "./node_modules/@any-listen/eslint/tsconfig.json", "compilerOptions": {"lib": ["ESNext"], "composite": true, "target": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "module": "ESNext", "types": ["@any-listen/types"], "baseUrl": "./src", "paths": {"@/*": ["./*"]}}, "include": ["src/**/*.ts", "src/**/*.js"], "exclude": ["**/node_modules/@types/node/globals.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}
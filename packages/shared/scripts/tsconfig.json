{
  "extends": "./node_modules/@any-listen/eslint/tsconfig.node.json",
  "compilerOptions": {
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "types": ["@any-listen/nodejs/node_modules/@types/node"],
    "outDir": "./dist"
  },
  "ts-node": {
    // "esm": true /* ... Line to be added ... */,
    "experimentalSpecifierResolution": "node",
    "compilerOptions": {
      "module": "CommonJS"
    },
    "include": ["**/*.d.ts", "**/*.ts", "**/*.js"]
  }
}

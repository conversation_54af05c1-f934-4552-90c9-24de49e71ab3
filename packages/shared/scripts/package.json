{"name": "@shared/scripts", "scripts": {"pack:win": "ts-node pack-desktop.ts && pnpm -F @shared/scripts pack:win:setup && pnpm -F @shared/scripts pack:win:7z", "pack:win:setup": "pnpm -F @shared/scripts pack:win:setup:x86_64 && pnpm -F @shared/scripts pack:win:setup:x64 && pnpm -F @shared/scripts pack:win:setup:x86 && pnpm -F @shared/scripts pack:win:setup:arm64", "pack:win:setup:x86_64": "pnpm -F desktop pack:win:setup:x86_64", "pack:win:setup:x64": "pnpm -F desktop pack:win:setup:x64", "pack:win:setup:x86": "pnpm -F desktop pack:win:setup:x86", "pack:win:setup:arm64": "pnpm -F desktop pack:win:setup:arm64", "pack:win:portable": "pnpm -F desktop pack:win:portable:x86_64 && pnpm -F desktop pack:win:portable:x64 && pnpm -F desktop pack:win:portable:x86", "pack:win:portable:x86_64": "pnpm -F desktop pack:win:portable:x86_64", "pack:win:portable:x64": "pnpm -F desktop pack:win:portable:x64", "pack:win:portable:x86": "pnpm -F desktop pack:win:portable:x86", "pack:win:7z": "pnpm -F @shared/scripts pack:win:7z:x64 && pnpm -F @shared/scripts pack:win:7z:arm64 && pnpm -F @shared/scripts pack:win7:7z:x64 && pnpm -F @shared/scripts pack:win7:7z:x86", "pack:win:7z:x64": "pnpm -F desktop pack:win:7z:x64", "pack:win:7z:arm64": "pnpm -F desktop pack:win:7z:arm64", "pack:win7:7z:x64": "pnpm -F desktop pack:win7:7z:x64", "pack:win7:7z:x86": "pnpm -F desktop pack:win7:7z:x86", "pack:linux": "pnpm -F @shared/scripts pack:linux:appImage && pnpm -F @shared/scripts pack:linux:deb && pnpm -F @shared/scripts pack:linux:rpm && pnpm -F @shared/scripts pack:linux:pacman", "pack:linux:appImage": "pnpm -F desktop pack:linux:appImage", "pack:linux:deb": "pnpm -F @shared/scripts pack:linux:deb:amd64 && pnpm -F @shared/scripts pack:linux:deb:arm64 && pnpm -F @shared/scripts pack:linux:deb:armv7l", "pack:linux:deb:amd64": "pnpm -F desktop pack:linux:deb:amd64", "pack:linux:deb:arm64": "pnpm -F desktop pack:linux:deb:arm64", "pack:linux:deb:armv7l": "pnpm -F desktop pack:linux:deb:armv7l", "pack:linux:rpm": "pnpm -F desktop pack:linux:rpm", "pack:linux:pacman": "pnpm -F desktop pack:linux:pacman", "pack:mac": "pnpm -F @shared/scripts pack:mac:dmg && pnpm -F @shared/scripts pack:mac:dmg:arm64", "pack:mac:dmg": "pnpm -F desktop pack:mac:dmg", "pack:mac:dmg:arm64": "pnpm -F desktop pack:mac:dmg:arm64", "publish:win:setup:x64": "pnpm -F desktop publish:win:setup:x64", "publish:win:setup:x86": "pnpm -F desktop publish:win:setup:x86", "publish:win:setup:arm64": "pnpm -F desktop publish:win:setup:arm64", "publish:win:setup:x86_64": "pnpm -F desktop publish:win:setup:x86_64", "publish:win:portable:x86_64": "pnpm -F desktop publish:win:portable:x86_64", "publish:win:portable:x64": "pnpm -F desktop publish:win:portable:x64", "publish:win:portable:x86": "pnpm -F desktop publish:win:portable:x86", "publish:win:7z:x64": "pnpm -F desktop publish:win:7z:x64", "publish:win:7z:arm64": "pnpm -F desktop publish:win:7z:arm64", "publish:win7:7z:x64": "pnpm -F desktop publish:win7:7z:x64", "publish:win7:7z:x86": "pnpm -F desktop publish:win7:7z:x86", "publish:mac:dmg": "pnpm -F desktop publish:mac:dmg", "publish:mac:dmg:arm64": "pnpm -F desktop publish:mac:dmg:arm64", "publish:linux:deb:amd64": "pnpm -F desktop publish:linux:deb:amd64", "publish:linux:deb:arm64": "pnpm -F desktop publish:linux:deb:arm64", "publish:linux:deb:armv7l": "pnpm -F desktop publish:linux:deb:armv7l", "publish:linux:appImage": "pnpm -F desktop publish:linux:appImage", "publish:linux:rpm": "pnpm -F desktop publish:linux:rpm", "publish:linux:pacman": "pnpm -F desktop publish:linux:pacman", "publish:desktop": "pnpm -F desktop publish", "publish:web": "pnpm -F web-server publish", "dev:desktop": "ts-node dev-desktop.ts", "dev:web": "ts-node dev-web.ts", "build:desktop:dir": "ts-node pack-desktop.ts && pnpm -F desktop pack:dir", "build:desktop": "ts-node pack-desktop.ts && pnpm -F desktop pack:win:setup:x64", "build:web": "ts-node pack-web.ts", "build": "ts-node pack.ts", "postinstall": "ts-node ./postinstall.ts"}, "devDependencies": {"@any-listen/common": "workspace:@shared/common@*", "@any-listen/desktop": "workspace:desktop@*", "@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/nodejs": "workspace:@shared/nodejs@*", "@any-listen/types": "workspace:@shared/types@*", "@any-listen/extension-preload": "workspace:@shared/extension-preload@*", "@any-listen/web-server": "workspace:web-server@*", "@any-listen/view-main": "workspace:view-main@*", "@types/spinnies": "^0.5.3", "changelog-parser": "^3.0.1", "del": "^8.0.0", "picocolors": "^1.1.1", "spinnies": "github:lyswhut/spinnies.git#233305c58694aa3b053e3ab9af9049993f918b9d", "tree-kill": "^1.2.2", "ts-node": "^10.9.2", "vite": "^6.3.5"}}
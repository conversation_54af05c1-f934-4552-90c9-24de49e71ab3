{"anylisten": "Any Listen", "app_name": "Any Listen", "batch_select": "Bulk selection", "batch_select_exit": "Exit multiple selection", "btn_cancel": "Cancel", "btn_clear": "Clear", "btn_close": "Closure", "btn_confirm": "Confirm", "btn_ok": "Already understood", "btn_remove": "Remove", "btn_select_all": "Select all", "btn_selected_single_tip": "{path} selected", "btn_selected_tip": "{num} item selected", "btn_submit": "Submit", "btn_unselect_all": "Uncheck", "cancel_button_text_2": "No, no, I clicked the wrong one", "close": "Closure", "confirm_button_text": "Yes", "database_verify_failed": "Database table structure verification failed", "database_verify_failed_detail": "Database table structure verification failed, we will back up the problematic database to: {backPath}\nIf this issue causes you to lose data, you can try to retrieve them from backup files.", "date_format_hour": "{num} hours ago", "date_format_minute": "{num} minutes ago", "date_format_second": "{num} seconds ago", "desktop_lyric__hide": "Hide desktop lyrics", "desktop_lyric__lock": "Lock desktop lyrics", "desktop_lyric__show": "Show desktop lyrics", "desktop_lyric__top_off": "Unpin desktop lyrics", "desktop_lyric__top_on": "Pinned desktop lyrics", "desktop_lyric__unlock": "Unlock desktop lyrics", "donate": "Donate ❤️", "duplicate_music": "Duplicate song", "edit_list_modal__edit_title": "Edit list", "edit_list_modal__form_list_name": "List name", "edit_list_modal__list_general": "Normal list", "edit_list_modal__list_local": "Local list", "edit_list_modal__list_online": "Online list", "edit_list_modal__new_title": "Create new list", "extension.header.actions.install_local": "Local installation", "extension.install_failed": "❌ Extension [{name}] Installation failed: {err}", "extension.install_local_failed.public_key_not_match": "Installation failed, the upcoming extension is inconsistent with the installed signature", "extension.install_success": "✔️ Extension [{name}] Installed successfully", "extension.select_local_file": "Select the extension installation package", "extension__action_disable": "Disable", "extension__action_enable": "Enable", "extension__action_install": "Install", "extension__action_uninstall": "Uninstall", "extension__action_update": "Update", "extension__actions": "Actions", "extension__grant_internet": "This extension can access the network", "extension__grant_music_list": "This extension can edit your favorite song list", "extension__grant_player": "This extension can control your player", "extension__install_local_failed": "Installation of the extension package failed: {msg}", "extension__install_local_success": "Installation successfully", "extension__type_disabled": "Disabled", "extension__type_enabled": "Enabled", "extension__type_installed": "Installed", "extension__type_online": "Extension Store", "extenstion": "Extenstion", "find_music": "Song search", "find_music_exit": "Exit search", "is_enabled": "Enabled", "list_name__default": "Listening list", "list_name__last_play": "Play history", "list_name__love": "Love list", "list_sort_modal_by_album": "Album name", "list_sort_modal_by_create_time": "Add time", "list_sort_modal_by_down": "Descending", "list_sort_modal_by_field": "Sort field", "list_sort_modal_by_name": "Song name", "list_sort_modal_by_random": "Random", "list_sort_modal_by_singer": "Singer name", "list_sort_modal_by_time": "Duration", "list_sort_modal_by_type": "Sort categories", "list_sort_modal_by_up": "Ascending", "list_sort_modal_by_update_time": "Change time", "lists__music_load_failed": "Song loading failed: {err}", "lists__music_move_failed": "Song move failed: {err}", "lists__music_remove_failed": "Song removal failed: {err}", "lists__music_update_failed": "Song update failed: {err}", "loading": "Loading...⏳", "login_devices_create_time": "First time login:", "login_devices_current_device": "Current session", "login_devices_id": "Session ID: {id}", "login_devices_ip": "IP address: {ip}", "login_devices_last_active": "Last login:", "login_devices_ua": "User agent string: {ua}", "login_label": "Enter login password", "logout": "Log out", "lyric__load_error": "Failed to obtain lyrics", "main_window__hide": "Hide main window", "main_window__show": "Show main window", "min": "Minimize", "music_add_modal__add_title": "Add {name} to...", "music_add_modal__move_title": "Move {name} to...", "music_add_modal__multiple_add_title": "Add selected {num} songs to...", "music_add_modal__multiple_move_title": "Move selected {num} songs to...", "music_add_modal_add_failed": "Add failed: {err}", "music_add_modal_add_success": "Have been added", "music_add_modal_move_failed": "Move failed: {err}", "music_add_modal_move_success": "Have moved", "music_album": "Album name", "music_list__dislike_music_singer_tip": "Do you really don't like the \"{name}\" of \"{singer}\"?", "music_list__dislike_music_tip": "Do you really don't like \"{name}\"?", "music_list__remove_music_tip": "Do you really want to remove the selected {len} songs?", "music_list__remove_tip_confirm_btn": "Yes, that's right", "music_love": "Favorite songs", "music_name": "Song name", "music_singer": "Artist", "music_time": "Duration", "music_unlove": "Cancel favorites", "my_list": "My list", "no_item": "The list turned out to be empty...", "online__search_type_album": "Album", "online__search_type_music": "Song", "online__search_type_singer": "<PERSON>", "online__search_type_songlist": "Songlist", "online__type_album": "Album", "online__type_leaderboard": "Leaderboard", "online__type_search": "Search", "online__type_singer": "<PERSON>", "online__type_songlist": "Songlist", "online_resources": "Online resources", "pagination__next": "Next page", "pagination__page": "Page {num}", "pagination__prev": "Previous page", "play_all": "Play all", "play_detail.hide_tip": "Hide the details page (right-click in the interface to quickly hide the details page)", "play_detail.music_album": "Album: ", "play_detail.music_name": "Song Name: ", "play_detail.music_singer": "Artist: ", "play_random": "Shuffle", "player__buffering": "Buffering...", "player__end": "Finished playing", "player__error": "Audio loading error, switch to the next song after 5 seconds", "player__geting_url": "Getting the song link...", "player__geting_url_delay_retry": "Service is busy, try again in {time} seconds...", "player__list": "Playlist", "player__loading": "Music loading...", "player__next": "Next song", "player__pause": "Pause", "player__paused": "Suspended", "player__play": "Play", "player__play_toggle_mode_list": "Play sequentially", "player__play_toggle_mode_list_loop": "List loop", "player__play_toggle_mode_off": "Disable", "player__play_toggle_mode_random": "List random", "player__play_toggle_mode_single_loop": "Single loop", "player__playback_preserves_pitch": "Pitch compensation", "player__playback_rate": "Current playback rate: {rate}x", "player__playing": "Playing...", "player__prev": "Previous song", "player__refresh_url": "URL invalid, refreshing URL...", "player__skip_next": "next track", "player__skip_prev": "previous piece", "player__sound_effect": "Sound settings (experimental)", "player__sound_effect_biquad_filter": "Equalizer", "player__sound_effect_biquad_filter_preset_classical": "Classical", "player__sound_effect_biquad_filter_preset_dance": "Dance", "player__sound_effect_biquad_filter_preset_electronic": "Electronic", "player__sound_effect_biquad_filter_preset_pop": "Pop", "player__sound_effect_biquad_filter_preset_rock": "Rock", "player__sound_effect_biquad_filter_preset_slow": "Slow", "player__sound_effect_biquad_filter_preset_soft": "Soft", "player__sound_effect_biquad_filter_preset_subwoofer": "Subwoofer", "player__sound_effect_biquad_filter_preset_vocal": "Vocal", "player__sound_effect_biquad_filter_save_btn": "Save default", "player__sound_effect_biquad_filter_save_input": "New preset...", "player__sound_effect_convolution": "Ambient reverb sound effects", "player__sound_effect_convolution_file_bright_hall": "Hall", "player__sound_effect_convolution_file_cardiod_35_10_spread": "Rock", "player__sound_effect_convolution_file_cinema_diningroom": "Cinema", "player__sound_effect_convolution_file_dining_living_true_stereo": "Dining Room", "player__sound_effect_convolution_file_feedback_spring": "Feedback Spring", "player__sound_effect_convolution_file_living_bedroom_leveled": "Bathroom", "player__sound_effect_convolution_file_matrix_1": "Matrix", "player__sound_effect_convolution_file_matrix_2": "Matrix 2", "player__sound_effect_convolution_file_s2_r4_bd": "Church", "player__sound_effect_convolution_file_s3_r1_bd": "Stereo", "player__sound_effect_convolution_file_spreader50_65ms": "Indoor", "player__sound_effect_convolution_file_telephone": "Telephone", "player__sound_effect_convolution_file_tim_omni_35_10_magnetic": "Rock 2", "player__sound_effect_convolution_main_gain": "Original audio gain", "player__sound_effect_convolution_send_gain": "Ambient sound effect gain", "player__sound_effect_enabled": "Enable", "player__sound_effect_features_tip": "Tip: \"Sound Effect Settings\" conflicts with \"Customized Audio Output Device\". After enabling sound effect settings, the audio output device will be reset to the default output device. This problem cannot be solved at present.", "player__sound_effect_panner": "3D stereo surround (headphones required)", "player__sound_effect_panner_sound_r": "Sound distance", "player__sound_effect_panner_sound_speed": "Orbital speed", "player__sound_effect_pitch_shifter": "Pitch up and down adjustment", "player__sound_effect_pitch_shifter_tip": "Since the rising and falling tone data are currently processed by JS, this will result in significant additional CPU usage.\n\nKnown issues:\nIf the CPU resources are insufficient, processing tasks will accumulate and sound abnormality will occur. At this time, you need to pause the playback for a period of time and wait for the accumulated tasks to be processed before playing again.", "player__sound_effect_reset_btn": "Reset", "player__stoped": "Stopped", "player__volume": "Current volume:", "player__volume_mute_label": "Mute", "player__volume_muted": "Muted", "playlist__history": "Play record", "playlist__queue": "Play queue", "quit": "Quit", "select_all": "Select all", "setting": "Settings", "settings.basic.play_bar_style": "Playbar style", "settings.basic.play_bar_style_center_btn": "Center control button", "settings.basic.play_bar_style_full": "Full width progress bar", "settings.basic.play_bar_style_middle": "Medium progress bar", "settings.basic.play_bar_style_mini": "Mini progress bar", "settings.common.transparent_window": "The main window uses the built-in rounded corners and shadows", "settings.common.transparent_window_desc": "After closing, the system window rounded corners and shadow styles will be used, and the software will take effect after restarting.", "settings.other": "Other settings", "settings.play_detail": "Play Details Page Settings", "settings.play_detail.delay_scroll": "Delay lyrics scroll", "settings.play_detail.dynamic_background": "Using dynamic background", "settings.play_detail.zoom_active_lrc": "Zoom the currently played lyrics", "settings.player.lyric_roma": "Show lyrics Roman tones (if available)", "settings.player.lyric_transition": "Show lyrics translation (if available)", "settings__about": "About Any Listen", "settings__about_p1": "Any Listen is a free and open source audio player that currently supports Windows, macOS, Linux and web platforms.", "settings__about_p2": "We'll keep it pure, and if it's helpful to you and you've got the cash, consider making a donation to support our work.", "settings__about_p3": "During use, if you have any suggestions or find problems, please feel free to raise an issue on GitHub.", "settings__about_p4": "Project homepage:", "settings__basic": "Basic settings", "settings__basic_animate": "Show switching animation", "settings__basic_control_btn_position": "Control button location", "settings__basic_control_btn_position_left": "Left", "settings__basic_control_btn_position_right": "Right", "settings__basic_font_size": "Font size", "settings__basic_font_size_14px": "Smaller", "settings__basic_font_size_15px": "Small", "settings__basic_font_size_16px": "Standard", "settings__basic_font_size_17px": "Big", "settings__basic_font_size_18px": "Larger", "settings__basic_font_size_19px": "Very big", "settings__basic_lang": "Language", "settings__basic_theme": "Theme", "settings__basic_tray": "Close the software without exiting it and minimizing it to the system tray", "settings__basic_window_size": "Window size", "settings__basic_window_size_big": "Large", "settings__basic_window_size_huge": "<PERSON>ge", "settings__basic_window_size_larger": "Larger", "settings__basic_window_size_medium": "Medium", "settings__basic_window_size_oversized": "Oversized", "settings__basic_window_size_small": "Small", "settings__basic_window_size_smaller": "Smaller", "settings__basic_window_size_title": "Set the window size", "settings__play_save_play_time": "Remember playback progress", "settings__play_startup_auto_play": "Play music automatically after starting the software", "settings__player": "Playback settings", "settings__security": "Safety", "settings__security_login_devices": "Signed-in device", "settings__type_app": "Software settings", "settings__type_extension": "Extension settings", "settings__type_logs": "Log output", "settings__update": "Update", "settings__update_allow_pre_release": "Enable pre-release version", "settings__update_allow_pre_release_desc": "Pre-release version refers to a version before the official release, which may contain some new features or fixes, but may also be unstable.", "settings__update_checking": "Checking for updates...", "settings__update_commit_date": "Commit date: ", "settings__update_commit_id": "Commit hash: ", "settings__update_current_label": "Current version: ", "settings__update_downloading": "Update is found and being downloaded...⏳", "settings__update_init": "Updating...", "settings__update_latest": "The app is up-to-date. Enjoy yourself!🥂", "settings__update_latest_label": "Latest version: ", "settings__update_new_version": "Found a new version. Hurry up and update~🚀🚀", "settings__update_open_version_modal_btn": "Open Update Window", "settings__update_progress": "Status: ", "settings__update_show_change_log": "Show changelog on first startup after update", "settings__update_show_change_log_desc": "In order to use this software more happily, we recommend reading the update log when using the new version to understand the changes in the software. At the same time, if you encounter problems, you can read the FAQ to find solutions.", "settings__update_try_auto_update": "Automatically attempt to download updates when a new version is found", "settings__update_unknown": "unknown", "settings__update_unknown_tip": "❓ Failed to fetch the latest version information. it is recommended to go to the About page to open the project release address to check whether the current version is the latest", "sort_music": "Song sorting", "submit": "submit", "theme_add": "Add a theme", "theme_auto": "Auto", "theme_auto_tip": "Right-click to open the light and dark theme settings window", "theme_black": "Black", "theme_blue": "Blue", "theme_blue2": "Purple Blue", "theme_blue_plus": "Blue Plus", "theme_china_ink": "China Ink", "theme_edit_modal__app_bg": "Apply background color", "theme_edit_modal__aside_color": "Sidebar button color", "theme_edit_modal__badge": "Label color", "theme_edit_modal__badge_primary": "Main color", "theme_edit_modal__badge_secondary": "Secondary Color", "theme_edit_modal__badge_tertiary": "Third color", "theme_edit_modal__bg_image": "Background picture", "theme_edit_modal__bg_image_add": "Add background image", "theme_edit_modal__bg_image_change": "Change background image", "theme_edit_modal__bg_image_remove": "remove background image", "theme_edit_modal__close_btn": "Close", "theme_edit_modal__control_btn": "Left control button color", "theme_edit_modal__copy": "Copy theme", "theme_edit_modal__dark": "Dark theme", "theme_edit_modal__dark_font": "Dark font", "theme_edit_modal__font": "Font color", "theme_edit_modal__hide_btn": "Hide play details page", "theme_edit_modal__main_bg": "Content area background color", "theme_edit_modal__min_btn": "Minimize", "theme_edit_modal__pick_cancel": "Reset", "theme_edit_modal__pick_color": "Choose color", "theme_edit_modal__pick_last_color": "Use previous color", "theme_edit_modal__pick_save": "Confirm", "theme_edit_modal__preview": "Preview theme", "theme_edit_modal__primary": "Theme color", "theme_edit_modal__remove": "Delete", "theme_edit_modal__remove_tip": "Do you really want to remove this thread?", "theme_edit_modal__save_new": "Save new", "theme_edit_modal__select_bg_file": "Choose a background image", "theme_edit_modal__title": "Edit topic", "theme_green": "Green", "theme_grey": "Grey", "theme_happy_new_year": "New Year", "theme_max_tip": "You can only add up to 20 themes, delete some and add more 😜", "theme_mid_autumn": "Mid-Autumn", "theme_ming": "<PERSON>", "theme_more_btn_show": "More theme", "theme_naruto": "<PERSON><PERSON><PERSON>", "theme_orange": "Orange", "theme_pink": "Pink", "theme_purple": "Purple", "theme_red": "Red", "theme_selector_modal__dark_title": "dark theme", "theme_selector_modal__light_title": "Bright theme", "theme_selector_modal__theme_name": "Topic name", "theme_selector_modal__title": "Follow system theme settings", "theme_selector_modal__title_tip": "Note: You can set a light theme and a dark theme in advance, and then it will automatically switch to the corresponding theme you set in advance according to the light and dark theme colors of the system.", "update_failed_tip": "Automatically downloading of the new version failed. You can try to download the update again or manually download the update.\n\nThe address of the new version is written under the update pop-up window. Download the new version and directly overwrite the installation. If the installation fails, please refer to the common problems to solve it.\n\nNote: Currently, only the Windows installation version can be automatically updated (Linux's AppImage and deb versions also seem to be OK, not tested). Please download and update other versions manually!", "update_modal.change_log": "Version changes: ", "update_modal.check_result_latest": "It's the latest version", "update_modal.check_result_new": "Discover a new version ✨", "update_modal.cur_progress": "Current download progress: ", "update_modal.current_version": "Current version: ", "update_modal.download": "Download update", "update_modal.downloaded_desc_1": "The new version has been downloaded.", "update_modal.downloaded_desc_2_1": "You can choose", "update_modal.downloaded_desc_2_2": "Restart update now", "update_modal.downloaded_desc_2_3": "Or later", "update_modal.downloaded_desc_2_4": "When closing the program", "update_modal.downloaded_desc_2_5": "Automatic update ~", "update_modal.downloaded_restart_btn": "Restart update now", "update_modal.downloaded_title": "🚀 Program Update 🚀", "update_modal.history_version": "Historical version:", "update_modal.ignore_ver": "Ignore this version", "update_modal.ignore_ver_cancel": "Cancel <PERSON>", "update_modal.latest_title": "🎉 It's the latest version 🎉", "update_modal.latest_version": "Latest version: ", "update_modal.new_ver_desc_1": "You can choose to update automatically or manually.", "update_modal.new_ver_desc_2_1": "Manual updates can be used", "update_modal.new_ver_desc_2_2": "Software Release Page", "update_modal.new_ver_desc_2_3": "download.", "update_modal.new_ver_title": "🌟 Discover the new version 🌟", "update_modal.recheck_update": "Recheck for updates", "update_modal.unknown_desc_1": "The update information failed to be obtained, which may be caused by inability to access GitHub. Please check for updates manually!", "update_modal.unknown_desc_2_1": "Check method: Open", "update_modal.unknown_desc_2_2": "Software Release Page", "update_modal.unknown_desc_2_3": ", view the published \"Latest\" ", "update_modal.unknown_desc_2_4": "Version number", "update_modal.unknown_desc_2_5": "Is it consistent with the current version ({ver}).", "update_modal.unknown_desc_3": "If it is consistent, ignore the pop-up window and close it directly; otherwise, please manually download the new version to update.", "update_modal.unknown_hide_tip_btn": "No reminder within a week", "update_modal.unknown_title": "❓ Failed to obtain the latest version information ❓", "update_modal.update_checking": "Checking for updates...", "update_modal.update_downloading": "Downloading update...", "update_modal.update_handing": "Processing update...", "user_list__add_local_file_failed": "U All chosen {num} songs all analyzed all analysis and failure", "user_list__add_local_file_folder_cancelled": "✖️ Canceled", "user_list__add_local_file_folder_end": "✔️ Added Completed", "user_list__add_local_file_success_part": "⚠️{count} of the selected {all} songs failed to parse", "user_list__add_local_file_successfull": "U The selected {num} song has been analyzed and added", "user_list__select_local_file": "Select song file", "user_list__select_local_file_folder": "Select a song folder", "user_list_menu__create": "Create new list", "user_list_menu__edit": "Edit list", "user_list_menu__remove": "Delete", "user_list_menu__select_local_file": "Add local songs", "user_list_menu__select_local_file_folder": "Add local songs (folder)", "user_list_menu__sync": "renew", "user_list_music_menu__add_to": "Add to...", "user_list_music_menu__copy_name": "Copy song title", "user_list_music_menu__detail": "Details", "user_list_music_menu__dislike": "Dislike", "user_list_music_menu__download": "Download", "user_list_music_menu__locate": "Locate song", "user_list_music_menu__move_to": "Move to...", "user_list_music_menu__play": "Play", "user_list_music_menu__play_later": "Play later", "user_list_music_menu__remove": "Remove", "user_list_music_menu__sort": "Adjust position"}
declare namespace AnyListen {
  interface ThemeColors {
    '--color-000': string
    '--color-050': string
    '--color-100': string
    '--color-150': string
    '--color-200': string
    '--color-250': string
    '--color-300': string
    '--color-350': string
    '--color-400': string
    '--color-450': string
    '--color-500': string
    '--color-550': string
    '--color-600': string
    '--color-650': string
    '--color-700': string
    '--color-750': string
    '--color-800': string
    '--color-850': string
    '--color-900': string
    '--color-950': string
    '--color-1000': string

    '--color-theme': string

    '--color-primary': string
    '--color-primary-alpha-100': string
    '--color-primary-alpha-200': string
    '--color-primary-alpha-300': string
    '--color-primary-alpha-400': string
    '--color-primary-alpha-500': string
    '--color-primary-alpha-600': string
    '--color-primary-alpha-700': string
    '--color-primary-alpha-800': string
    '--color-primary-alpha-900': string

    '--color-primary-dark-100': string
    '--color-primary-dark-100-alpha-100': string
    '--color-primary-dark-100-alpha-200': string
    '--color-primary-dark-100-alpha-300': string
    '--color-primary-dark-100-alpha-400': string
    '--color-primary-dark-100-alpha-500': string
    '--color-primary-dark-100-alpha-600': string
    '--color-primary-dark-100-alpha-700': string
    '--color-primary-dark-100-alpha-800': string
    '--color-primary-dark-100-alpha-900': string

    '--color-primary-dark-200': string
    '--color-primary-dark-200-alpha-100': string
    '--color-primary-dark-200-alpha-200': string
    '--color-primary-dark-200-alpha-300': string
    '--color-primary-dark-200-alpha-400': string
    '--color-primary-dark-200-alpha-500': string
    '--color-primary-dark-200-alpha-600': string
    '--color-primary-dark-200-alpha-700': string
    '--color-primary-dark-200-alpha-800': string
    '--color-primary-dark-200-alpha-900': string

    '--color-primary-dark-300': string
    '--color-primary-dark-300-alpha-100': string
    '--color-primary-dark-300-alpha-200': string
    '--color-primary-dark-300-alpha-300': string
    '--color-primary-dark-300-alpha-400': string
    '--color-primary-dark-300-alpha-500': string
    '--color-primary-dark-300-alpha-600': string
    '--color-primary-dark-300-alpha-700': string
    '--color-primary-dark-300-alpha-800': string
    '--color-primary-dark-300-alpha-900': string

    '--color-primary-dark-400': string
    '--color-primary-dark-400-alpha-100': string
    '--color-primary-dark-400-alpha-200': string
    '--color-primary-dark-400-alpha-300': string
    '--color-primary-dark-400-alpha-400': string
    '--color-primary-dark-400-alpha-500': string
    '--color-primary-dark-400-alpha-600': string
    '--color-primary-dark-400-alpha-700': string
    '--color-primary-dark-400-alpha-800': string
    '--color-primary-dark-400-alpha-900': string

    '--color-primary-dark-500': string
    '--color-primary-dark-500-alpha-100': string
    '--color-primary-dark-500-alpha-200': string
    '--color-primary-dark-500-alpha-300': string
    '--color-primary-dark-500-alpha-400': string
    '--color-primary-dark-500-alpha-500': string
    '--color-primary-dark-500-alpha-600': string
    '--color-primary-dark-500-alpha-700': string
    '--color-primary-dark-500-alpha-800': string
    '--color-primary-dark-500-alpha-900': string

    '--color-primary-dark-600': string
    '--color-primary-dark-600-alpha-100': string
    '--color-primary-dark-600-alpha-200': string
    '--color-primary-dark-600-alpha-300': string
    '--color-primary-dark-600-alpha-400': string
    '--color-primary-dark-600-alpha-500': string
    '--color-primary-dark-600-alpha-600': string
    '--color-primary-dark-600-alpha-700': string
    '--color-primary-dark-600-alpha-800': string
    '--color-primary-dark-600-alpha-900': string

    '--color-primary-dark-700': string
    '--color-primary-dark-700-alpha-100': string
    '--color-primary-dark-700-alpha-200': string
    '--color-primary-dark-700-alpha-300': string
    '--color-primary-dark-700-alpha-400': string
    '--color-primary-dark-700-alpha-500': string
    '--color-primary-dark-700-alpha-600': string
    '--color-primary-dark-700-alpha-700': string
    '--color-primary-dark-700-alpha-800': string
    '--color-primary-dark-700-alpha-900': string

    '--color-primary-dark-800': string
    '--color-primary-dark-800-alpha-100': string
    '--color-primary-dark-800-alpha-200': string
    '--color-primary-dark-800-alpha-300': string
    '--color-primary-dark-800-alpha-400': string
    '--color-primary-dark-800-alpha-500': string
    '--color-primary-dark-800-alpha-600': string
    '--color-primary-dark-800-alpha-700': string
    '--color-primary-dark-800-alpha-800': string
    '--color-primary-dark-800-alpha-900': string

    '--color-primary-dark-900': string
    '--color-primary-dark-900-alpha-100': string
    '--color-primary-dark-900-alpha-200': string
    '--color-primary-dark-900-alpha-300': string
    '--color-primary-dark-900-alpha-400': string
    '--color-primary-dark-900-alpha-500': string
    '--color-primary-dark-900-alpha-600': string
    '--color-primary-dark-900-alpha-700': string
    '--color-primary-dark-900-alpha-800': string
    '--color-primary-dark-900-alpha-900': string

    '--color-primary-dark-1000': string
    '--color-primary-dark-1000-alpha-100': string
    '--color-primary-dark-1000-alpha-200': string
    '--color-primary-dark-1000-alpha-300': string
    '--color-primary-dark-1000-alpha-400': string
    '--color-primary-dark-1000-alpha-500': string
    '--color-primary-dark-1000-alpha-600': string
    '--color-primary-dark-1000-alpha-700': string
    '--color-primary-dark-1000-alpha-800': string
    '--color-primary-dark-1000-alpha-900': string

    '--color-primary-light-100': string
    '--color-primary-light-100-alpha-100': string
    '--color-primary-light-100-alpha-200': string
    '--color-primary-light-100-alpha-300': string
    '--color-primary-light-100-alpha-400': string
    '--color-primary-light-100-alpha-500': string
    '--color-primary-light-100-alpha-600': string
    '--color-primary-light-100-alpha-700': string
    '--color-primary-light-100-alpha-800': string
    '--color-primary-light-100-alpha-900': string

    '--color-primary-light-200': string
    '--color-primary-light-200-alpha-100': string
    '--color-primary-light-200-alpha-200': string
    '--color-primary-light-200-alpha-300': string
    '--color-primary-light-200-alpha-400': string
    '--color-primary-light-200-alpha-500': string
    '--color-primary-light-200-alpha-600': string
    '--color-primary-light-200-alpha-700': string
    '--color-primary-light-200-alpha-800': string
    '--color-primary-light-200-alpha-900': string

    '--color-primary-light-300': string
    '--color-primary-light-300-alpha-100': string
    '--color-primary-light-300-alpha-200': string
    '--color-primary-light-300-alpha-300': string
    '--color-primary-light-300-alpha-400': string
    '--color-primary-light-300-alpha-500': string
    '--color-primary-light-300-alpha-600': string
    '--color-primary-light-300-alpha-700': string
    '--color-primary-light-300-alpha-800': string
    '--color-primary-light-300-alpha-900': string

    '--color-primary-light-400': string
    '--color-primary-light-400-alpha-100': string
    '--color-primary-light-400-alpha-200': string
    '--color-primary-light-400-alpha-300': string
    '--color-primary-light-400-alpha-400': string
    '--color-primary-light-400-alpha-500': string
    '--color-primary-light-400-alpha-600': string
    '--color-primary-light-400-alpha-700': string
    '--color-primary-light-400-alpha-800': string
    '--color-primary-light-400-alpha-900': string

    '--color-primary-light-500': string
    '--color-primary-light-500-alpha-100': string
    '--color-primary-light-500-alpha-200': string
    '--color-primary-light-500-alpha-300': string
    '--color-primary-light-500-alpha-400': string
    '--color-primary-light-500-alpha-500': string
    '--color-primary-light-500-alpha-600': string
    '--color-primary-light-500-alpha-700': string
    '--color-primary-light-500-alpha-800': string
    '--color-primary-light-500-alpha-900': string

    '--color-primary-light-600': string
    '--color-primary-light-600-alpha-100': string
    '--color-primary-light-600-alpha-200': string
    '--color-primary-light-600-alpha-300': string
    '--color-primary-light-600-alpha-400': string
    '--color-primary-light-600-alpha-500': string
    '--color-primary-light-600-alpha-600': string
    '--color-primary-light-600-alpha-700': string
    '--color-primary-light-600-alpha-800': string
    '--color-primary-light-600-alpha-900': string

    '--color-primary-light-700': string
    '--color-primary-light-700-alpha-100': string
    '--color-primary-light-700-alpha-200': string
    '--color-primary-light-700-alpha-300': string
    '--color-primary-light-700-alpha-400': string
    '--color-primary-light-700-alpha-500': string
    '--color-primary-light-700-alpha-600': string
    '--color-primary-light-700-alpha-700': string
    '--color-primary-light-700-alpha-800': string
    '--color-primary-light-700-alpha-900': string

    '--color-primary-light-800': string
    '--color-primary-light-800-alpha-100': string
    '--color-primary-light-800-alpha-200': string
    '--color-primary-light-800-alpha-300': string
    '--color-primary-light-800-alpha-400': string
    '--color-primary-light-800-alpha-500': string
    '--color-primary-light-800-alpha-600': string
    '--color-primary-light-800-alpha-700': string
    '--color-primary-light-800-alpha-800': string
    '--color-primary-light-800-alpha-900': string

    '--color-primary-light-900': string
    '--color-primary-light-900-alpha-100': string
    '--color-primary-light-900-alpha-200': string
    '--color-primary-light-900-alpha-300': string
    '--color-primary-light-900-alpha-400': string
    '--color-primary-light-900-alpha-500': string
    '--color-primary-light-900-alpha-600': string
    '--color-primary-light-900-alpha-700': string
    '--color-primary-light-900-alpha-800': string
    '--color-primary-light-900-alpha-900': string

    '--color-primary-light-1000': string
    '--color-primary-light-1000-alpha-100': string
    '--color-primary-light-1000-alpha-200': string
    '--color-primary-light-1000-alpha-300': string
    '--color-primary-light-1000-alpha-400': string
    '--color-primary-light-1000-alpha-500': string
    '--color-primary-light-1000-alpha-600': string
    '--color-primary-light-1000-alpha-700': string
    '--color-primary-light-1000-alpha-800': string
    '--color-primary-light-1000-alpha-900': string
  }

  interface Theme {
    id: string
    name: string
    isDark: boolean
    isCustom: boolean
    config: {
      themeColors: ThemeColors
      extInfo: {
        '--color-app-background': string
        '--color-main-background': string
        // '--color-nav-font': string
        '--background-image': string
        '--background-image-position': string
        '--background-image-size': string
        '--background-blur': string

        // 关闭按钮颜色
        '--color-btn-hide': string
        '--color-btn-min': string
        '--color-btn-close': string

        // 徽章颜色
        '--color-badge-primary': string
        '--color-badge-secondary': string
        '--color-badge-tertiary': string
      }
    }
  }

  interface ThemeInfo {
    themes: Theme[]
    userThemes: Theme[]
    dataPath: string
  }

  interface ThemeSetting {
    id: string
    name: string
    isDark: boolean
    colors: Record<string, string>
  }

  interface ThemeList {
    themes: Theme[]
    userThemes: Theme[]
  }
}

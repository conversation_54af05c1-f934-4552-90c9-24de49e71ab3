{"name": "@shared/app", "types": "./types.d.ts", "type": "module", "dependencies": {"@any-listen/common": "workspace:@shared/common@*", "@any-listen/i18n": "workspace:@shared/i18n@*", "@any-listen/nodejs": "workspace:@shared/nodejs@*", "@any-listen/theme": "workspace:@shared/theme@*", "@any-listen/web": "workspace:@shared/web@*", "better-sqlite3": "^11.10.0", "iconv-lite": "^0.6.3", "jschardet": "^3.1.4", "jsonschema": "^1.5.0", "message2call": "^2.0.3", "music-metadata": "^11.2.3", "ws": "^8.18.2"}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/types": "workspace:@shared/types@*", "@types/better-sqlite3": "^7.6.13", "@types/tar": "^6.1.13", "vite": "^6.3.5"}}
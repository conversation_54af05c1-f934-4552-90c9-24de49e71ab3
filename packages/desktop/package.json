{"version": "0.1.0-alpha.0", "name": "desktop", "main": "build-config/vite.config.ts", "scripts": {"pack:win:setup:x86_64": "node build-config/build-pack.cjs target=win arch=x86_64 type=setup", "pack:win:setup:x64": "node build-config/build-pack.cjs target=win arch=x64 type=setup", "pack:win:setup:x86": "node build-config/build-pack.cjs target=win arch=x86 type=setup", "pack:win:setup:arm64": "node build-config/build-pack.cjs target=win arch=arm64 type=setup", "pack:win:portable:x86_64": "node build-config/build-pack.cjs target=win arch=x86_64 type=portable", "pack:win:portable:x64": "node build-config/build-pack.cjs target=win arch=x64 type=portable", "pack:win:portable:x86": "node build-config/build-pack.cjs target=win arch=x86 type=portable", "pack:win:7z": "npm run pack:win:7z:x64", "pack:win:7z:x64": "node build-config/build-pack.cjs target=win arch=x64 type=green", "pack:win:7z:arm64": "node build-config/build-pack.cjs target=win arch=arm64 type=green", "pack:win7:7z:x64": "node build-config/build-pack.cjs target=win arch=x64 type=win7_green", "pack:win7:7z:x86": "node build-config/build-pack.cjs target=win arch=x86 type=win7_green", "pack:linux:appImage": "node build-config/build-pack.cjs target=linux arch=x64 type=appImage", "pack:linux:deb:amd64": "node build-config/build-pack.cjs target=linux arch=x64 type=deb", "pack:linux:deb:arm64": "node build-config/build-pack.cjs target=linux arch=arm64 type=deb", "pack:linux:deb:armv7l": "node build-config/build-pack.cjs target=linux arch=armv7l type=deb", "pack:linux:rpm": "node build-config/build-pack.cjs target=linux arch=x64 type=rpm", "pack:linux:pacman": "node build-config/build-pack.cjs target=linux arch=x64 type=pacman", "pack:mac:dmg": "node build-config/build-pack.cjs target=mac arch=x64 type=dmg", "pack:mac:dmg:arm64": "node build-config/build-pack.cjs target=mac arch=arm64 type=dmg", "pack:dir": "node build-config/build-pack.cjs target=dir", "publish:win:setup:x64": "node build-config/build-pack.cjs target=win arch=x64 type=setup publish=always", "publish:win:setup:x86": "node build-config/build-pack.cjs target=win arch=x86 type=setup publish=always", "publish:win:setup:arm64": "node build-config/build-pack.cjs target=win arch=arm64 type=setup publish=always", "publish:win:setup:x86_64": "node build-config/build-pack.cjs target=win arch=x86_64 type=setup publish=always", "publish:win:portable:x86_64": "node build-config/build-pack.cjs target=win arch=x86_64 type=portable publish=always", "publish:win:portable:x64": "node build-config/build-pack.cjs target=win arch=x64 type=portable publish=always", "publish:win:portable:x86": "node build-config/build-pack.cjs target=win arch=x86 type=portable publish=always", "publish:win:7z:x64": "node build-config/build-pack.cjs target=win arch=x64 type=green publish=always", "publish:win:7z:arm64": "node build-config/build-pack.cjs target=win arch=arm64 type=green publish=always", "publish:win7:7z:x64": "node build-config/build-pack.cjs target=win arch=x64 type=win7_green publish=always", "publish:win7:7z:x86": "node build-config/build-pack.cjs target=win arch=x86 type=win7_green publish=always", "publish:mac:dmg": "node build-config/build-pack.cjs target=mac arch=x64 type=dmg publish=always", "publish:mac:dmg:arm64": "node build-config/build-pack.cjs target=mac arch=arm64 type=dmg publish=always", "publish:linux:deb:amd64": "node build-config/build-pack.cjs target=linux arch=x64 type=deb publish=always", "publish:linux:deb:arm64": "node build-config/build-pack.cjs target=linux arch=arm64 type=deb publish=always", "publish:linux:deb:armv7l": "node build-config/build-pack.cjs target=linux arch=armv7l type=deb publish=always", "publish:linux:appImage": "node build-config/build-pack.cjs target=linux arch=x64 type=appImage publish=always", "publish:linux:rpm": "node build-config/build-pack.cjs target=linux arch=x64 type=rpm publish=always", "publish:linux:pacman": "node build-config/build-pack.cjs target=linux arch=x64 type=pacman publish=always", "publish": "node publish", "build": "vite build", "rebuild:deps": "node ./build-config/before-install.cjs && electron-builder install-app-deps && node ./build-config/post-install.cjs"}, "repository": {"type": "git", "url": "git+https://github.com/any-listen/any-listen-desktop.git"}, "dependencies": {"@any-listen/app": "workspace:@shared/app@*", "@any-listen/common": "workspace:@shared/common@*", "@any-listen/i18n": "workspace:@shared/i18n@*", "@any-listen/nodejs": "workspace:@shared/nodejs@*", "@any-listen/theme": "workspace:@shared/theme@*", "better-sqlite3": "^11.10.0", "electron-log": "5.4.0", "electron-updater": "^6.6.4", "font-list": "^1.5.1", "message2call": "^2.0.3", "undici": "^7.10.0"}, "devDependencies": {"@any-listen/eslint": "workspace:@shared/eslint@*", "@any-listen/types": "workspace:@shared/types@*", "electron": "^34.5.6", "electron-builder": "^26.0.15", "electron-debug": "^4.1.0", "electron-to-chromium": "^1.5.159", "node-abi": "^4.9.0", "prebuild-install": "^7.1.3", "tar": "^7.4.3", "vite": "^6.3.5"}}